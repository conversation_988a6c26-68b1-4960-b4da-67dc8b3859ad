#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-07-18 16:07:57
@Desc: 
@Server: 
"""
from pydantic import BaseModel, model_validator, field_validator


class TagsTrade(BaseModel):
    """
    海关贸易数据标签 模型
    """
    trade_uuid: str
    product_name: str = ''
    product_material: str = ''
    product_specification: str = ''
    product_form: str = ''
    product_quantity: int = 0
    product_quantity_unit: str = ''
    product_function: str = ''
    product_package: str = ''
    product_hs_code: str = ''
    brand_name: str = ''
    product_category: str = ''
    product_tagged_keywords: str = ''
    transport_mode_code: int = 0
    trade_weight: float = 0
    trade_amount: float = 0
    likely_sensitive_goods: int = 0
    product_alias: str = ''
    product_superordinate: str = ''
    product_downstream: str = ''

    @field_validator(
        "product_name", "product_material", "product_specification", "product_form", "product_quantity_unit",
        "product_function", "product_languages_code", "product_package", "product_hs_code", "brand_name",
        "manufacturer_name", "product_category", "product_tagged_keywords", "language_detected", "product_alias",
        "product_superordinate", "product_downstream",
        mode="before"
    )
    @classmethod
    def replace_null_string(cls, v):
        if isinstance(v, str) and v.strip().lower() == "null":
            return ""
        return v

    @field_validator(
        "product_quantity", "transport_mode_code", "trade_weight", "trade_amount", "likely_sensitive_goods",
        mode="before"
    )
    @classmethod
    def replace_null_string(cls, v):
        if isinstance(v, str) and v.strip().lower() == "null":
            return 0
        return v


class TagsTradeTest(BaseModel):
    """
    海关贸易数据标签测试 模型
    """
    trade_uuid: str
    model_name: str = ''
    model_tokens: int = 0
    completion_tokens: int = 0
    prompt_tokens: int = 0
    time_consuming: float = 0
    source_product_desc: str = ''
    source_product_hs_code: str = ''
    source_product_quantity: int = 0
    source_product_quantity_unit: str = ''
    source_trade_date: str = ''
    source_seller_country: str = ''
    source_buyer_country: str = ''
    source_seller_name: str = ''
    source_buyer_name: str = ''
    source_trade_weight: float = 0
    source_trade_weight_unit: str = ''
    source_trade_amount: float = 0
    source_trade_amount_unit: str = ''
    source_transport_mode: str = ''
    product_name: str = ''
    product_material: str = ''
    product_specification: str = ''
    product_form: str = ''
    product_quantity: int = 0
    product_quantity_unit: str = ''
    product_function: str = ''
    product_languages_code: str = ''
    product_package: str = ''
    product_hs_code: str = ''
    brand_name: str = ''
    manufacturer_name: str = ''
    product_category: str = ''
    product_tagged_keywords: str = ''
    seller_country_code: str = ''
    buyer_country_code: str = ''
    transport_mode_code: int = 0
    trade_weight: float = 0
    trade_amount: float = 0
    seller_name_en: str = ''
    buyer_name_en: str = ''
    manufacturer_name_en: str = ''
    language_detected: str = ''
    likely_sensitive_goods: int = 0
    product_alias: str = ''
    product_superordinate: str = ''
    product_downstream: str = ''