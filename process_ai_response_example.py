#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-08-08
@Desc: 处理AI返回数据并写入Excel的示例
"""

from tmp_tags_trade_test_v2 import TagsTradeTest
import json

def main():
    """
    示例：处理AI返回的JSON数据并写入Excel
    """
    # 创建TagsTradeTest实例
    processor = TagsTradeTest()
    
    # 你提供的AI返回数据
    ai_response_json = '''{"product_name": ["Lab Grown Diamond"], "material": "Lab Grown Diamond", "specification": "CUT AND POLISHED, FANCY INTENSE YELLOW VS1 IGI LG662484470 CVD", "product_form": "Piece", "product_quantity": 2, "product_quantity_unit": "", "product_function": "Jewelry Making", "product_package": "Piece", "product_hs_code": "710491", "brand_name": "NIVODA", "manufacturer_name": "NIVODA HK LIMITED", "product_category": "Jewelry", "product_tagged_keywords": ["Lab Grown Diamond", "CUT AND POLISHED", "FANCY INTENSE YELLOW", "VS1", "IGI LG662484470 CVD"], "transport_mode_code": null, "trade_weight": 0, "trade_amount": 397, "likely_sensitive_goods": false, "alias": ["Synthetic Diamond", "Lab Created Diamond", "Man-Made Diamond", "Cultured Diamond", "Engineered Diamond"], "superordinate": ["Carbon", "Chemical Vapor Deposition (CVD) Materials"], "downstream": ["Rings", "Necklaces", "Earrings", "Bracelets", "Pendants"]}'''
    
    # 处理数据并写入Excel
    trade_uuid = "021754616969175d24e408a3ff217b55cf08c9e1c6543633412d9"  # 使用你日志中的实际UUID
    
    success = processor.process_ai_response_to_excel(ai_response_json, trade_uuid)
    
    if success:
        print(f"✅ 成功处理AI响应数据并写入Excel！")
        print(f"📄 数据已保存到: out_data.xlsx")
        print(f"🔑 Trade UUID: {trade_uuid}")
    else:
        print("❌ 处理AI响应数据失败！")

def batch_process_example():
    """
    批量处理多个AI响应的示例，包含不同数据格式
    """
    processor = TagsTradeTest()

    # 模拟多个AI响应数据，包含列表类型的product_function
    ai_responses = [
        {
            "uuid": "uuid_001",
            "response": '''{"product_name": ["Lab Grown Diamond"], "material": "Lab Grown Diamond", "specification": "CUT AND POLISHED, FANCY INTENSE YELLOW VS1", "product_form": "Piece", "product_quantity": 2, "product_quantity_unit": "", "product_function": "Jewelry Making", "product_package": "Piece", "product_hs_code": "710491", "brand_name": "NIVODA", "manufacturer_name": "NIVODA HK LIMITED", "product_category": "Jewelry", "product_tagged_keywords": ["Lab Grown Diamond", "CUT AND POLISHED"], "transport_mode_code": null, "trade_weight": 0, "trade_amount": 397, "likely_sensitive_goods": false, "alias": ["Synthetic Diamond"], "superordinate": ["Carbon"], "downstream": ["Rings"]}'''
        },
        {
            "uuid": "uuid_002",
            "response": '''{"product_name": ["Cotton T-Shirt"], "material": "Cotton", "specification": "Size M, 100% Cotton", "product_form": "Piece", "product_quantity": 50, "product_quantity_unit": "pieces", "product_function": "Clothing", "product_package": "Carton", "product_hs_code": "610910", "brand_name": "Generic", "manufacturer_name": null, "product_category": "Apparel", "product_tagged_keywords": ["Cotton", "T-Shirt", "Clothing"], "transport_mode_code": 1, "trade_weight": 5.5, "trade_amount": 250, "likely_sensitive_goods": false, "alias": ["Tee", "Shirt"], "superordinate": ["Textile", "Cotton Fiber"], "downstream": ["Retail", "Fashion"]}'''
        },
        {
            "uuid": "uuid_003",
            "response": '''{"product_name": ["Door Seal"], "material": "Rubber", "specification": "18x15mm", "product_form": "Strip", "product_quantity": 100, "product_quantity_unit": "meters", "product_function": ["Door and window installation", "Lighting", "Waterproofing"], "product_package": "Roll", "product_hs_code": "401699", "brand_name": "Generic", "manufacturer_name": null, "product_category": "Sealing Parts", "product_tagged_keywords": ["Door", "Seal", "Rubber"], "transport_mode_code": 1, "trade_weight": 5.2, "trade_amount": 150, "likely_sensitive_goods": false, "alias": ["Weather Strip", "Door Gasket"], "superordinate": ["Rubber", "Polymer"], "downstream": ["Construction", "Automotive"]}'''
        }
    ]
    
    success_count = 0
    for item in ai_responses:
        success = processor.process_ai_response_to_excel(item["response"], item["uuid"])
        if success:
            success_count += 1
            print(f"✅ 成功处理 {item['uuid']}")
        else:
            print(f"❌ 处理失败 {item['uuid']}")
    
    print(f"\n📊 批量处理完成: {success_count}/{len(ai_responses)} 成功")

if __name__ == '__main__':
    print("=== AI响应数据处理示例 ===")
    main()
    
    print("\n=== 批量处理示例 ===")
    batch_process_example()
