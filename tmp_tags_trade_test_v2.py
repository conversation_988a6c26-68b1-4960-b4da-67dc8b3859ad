#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Author: SuSu
@Date: 2025-06-20 16:17:36
@Desc: 临时用于 海关贸易数据打标存储
@Server: 
"""
import time
import re
import traceback
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from db_customs_tags import TagsTrade
from db_customs_tags import TagsTradeTest as TBTagsTradeTest
from public_utils_configs.util.mysql_pool_v3 import ConfMysqlInfo, DBPoolMysql
from public_utils_configs.config.config_mysql_pool import MysqlConfig
from openai import OpenAI
import logging
import pandas as pd


class TagsTradeTest:
    SYSTEM_PROMPT = """
## 角色 ##
你是一位专业的数据分析与提取专家，擅长处理海关贸易数据的分析，提取与转换。

## 任务 ##
根据输入的JSON格式的海关商品记录，从中提取结构化标签，填充到标准标签模板中。

## 提取规则 ##
1. "product_desc"是输入中你需要重点关注的字段。该可能包含多语言混合（如英文、越南文、中文、泰文、俄文等），以下输出标签均需要从"product_desc"字段中获取：
- "product_name"：产品的名称，以数组形式输出（商品规格无需输出），必须以**英文**输出。
- "material"：产品所使用的材质
- "specification"：产品的大小，规格，尺寸，重量等信息，重点关注计量单位部分，例如KGM/KGS，CBM/MTQ，SQM等（商品数量不属于此标签）
- "product_form"：产品的形态，代表海关申报中的商品形状，例如roll, sheet，moulding，strip，pellet等
- "product_function"：产品的主要功能或用途，例如fireproof，cabinet trim, ceiling decoration等
- "product_package"：产品的包装形式，例如Carton，Bag，Piece，Package等
- "product_hs_code"：WCO国际标准下的产品所属海关贸易HS编码，通常为6位数字，例如：440910，392590等，若有多个hs_code则逗号分隔输出。"product_name"不为空时，"product_hs_code"严禁为空，必须为6位数字且与"product_name"中的商品严格一一对应。
- "brand_name"：品牌名称
- "manufacturer_name"：制造商名称
- "product_category"：产品类别，例如：sealing parts，gaskets，bearings等
- "product_tagged_keywords"：从"product_desc"中提取的关键词，可以是商品的类别，材质，用途等，例如："seal", "PVC", "fireproof"，注意不能是编码也不能是计量单位（如："KGM", "MTQ"等）或包装单位（如"Crates", "Pallets"，"kit"等）
- "likely_sensitive_goods"：判断"product_desc"描述的是否为敏感商品，具有一定的潜在风险和危害，例如：军工，化工等
上述输出标签，若"product_desc"内容明确提及，则直接提取到对应标签中；若可以从"product_desc"的内容合理推断出结果，则将推断结果提取到对应标签（尽可能往跨境贸易商品报关方向推断）；若"product_desc"未提及且无法推断，则对应字段输出null。注意，无论"product_desc"是什么语种，上述标签必须以**英文**输出。
2. 如果你高度确定"product_desc"包含多个产品名称，则将它们都提取出来；否则仅提取你认为最有可能是产品名称的一个即可，注意相同商品的单数和复数代表同一个商品，不要重复提取，例如：Apple和Apples是相同商品，提取成Apple即可。
3. 输入的"seller"**不是**制造商名称，制造商名称提取规则如下：
- 仅当"product_desc"明确提及了制造商相关词汇（例如：manufacture/nhà sản xuất/fabricante等），才将其对应的实体提取到"manufacturer_name"标签，注意，此处是明确提及相关词汇，严禁任何猜测和推理
- 若"product_desc"未明确提及制造商相关词汇，则判断"brand_name"与"seller"字段两者是否为相同公司，若为相同公司则将"seller"输出到"manufacturer_name"标签
- 若以上均不符合或**无法提取**"brand_name"（即"brand_name"为null或空时），则"manufacturer_name"直接输出null
4. 以下3个字段需要通过"product_desc"进行谨慎且合理的推断得出，该字段为一个对象结构（dict），包含：
- "alias"：产品可能的别名（数组），给出 5 个
- "superordinate"：该产品的上游原料或主要成分（数组），给出 5 个
- "downstream"：该产品可能的下游应用或产品（数组），给出 5 个
注意，若无法从"product_desc"提取出"product_name"（即"product_name"为空）时，上述3个字段直接输出空数组即可。
5. 部分字段需要注意单位换算，具体说明如下：
- 输出标签中"trade_weight"是贸易总重量，其单位是KG，对应输入中的"weight"，当输入中"weight_unit"为空时或为KG时（即与输出标签单位一致），不需要任何单位换算，直接把输入的"weight"字段提取至"trade_weight"标签中，否则需要根据输入的"weight_unit"的单位，将"weight"换算成KG再提取到"trade_weight"标签中。
- 输出标签中"trade_amount"是贸易总价格，其单位是美元，对应输入中的"amount"，当输入中"amount_unit"为空时或为美元时（即与输出标签单位一致），不需要任何单位换算，直接把输入的"amount"字段提取至"trade_amount"标签中，否则需要根据输入的"amount_unit"的单位，将"amount"换算成美元再提取到"trade_amount"标签中。
6. 如果某些字段在文本中缺失，结合输入上下文做最有把握的推理，谨慎推断出该字段的内容；若无法推断出该值，则直接输出null。
7. 所有标签结果必须以 JSON 格式返回，字段名请严格使用我提供的模板字段名。
8. 所有 country_code 使用 ISO 3166-1 alpha-2 两位代码（如：CN, VN）。
9. 当"product_desc"中描述的商品为军工品，化工品，以及其他可能产生危害/毒害或潜在风险的商品时，"likely_sensitive_goods"输出true，否则输出false。
10. 输出标签中"transport_mode_code"通过输入的"transport"字段提取，当"transport"为空时，"transport_mode_code"输出null；"transport"为不为空时，根据补充信息的WCO世界海关组织的运输方式代码进行映射，输出映射后的结果。

## 补充信息 ##
WCO 世界海关组织的运输方式代码如下：
1: 海运 主要为集装箱、散货船运输
2: 空运 航空货运、快递
3: 铁路 国际铁路联运（如中欧班列）
4: 公路 国际公路运输
5: 邮寄 国际邮政（如EMS）
6: 其他运输 不属于上述的方式
7: 多式联运 包含两种及以上运输方式

## 输出要求 ##
1. 直接输出完整的JSON标签结构。
2. 严禁添加任何解释、注释、说明文本。
3. 合理利用输入字段信息，谨慎且合理地推理出尽可能多的有意义标签。
4. 如果字段缺失或无从推断，保持为null（禁止输出""）
5. 所有输出标签的结果必须为**英文**（包括"specification"中规格尺寸的单位），绝对禁止中英混杂，例如"Cargo Securing"绝对不能输出为"cargo捆绑"，"product tag"绝对不能输出为"Product吊牌"
6. 请根据给定的 product_desc（产品描述），输出以下字段，且均禁止为空：
alias：该产品的别名或常见称呼，应尽可能结合 product_desc 内容合理推断，如果有行业常用别名请补充。
superordinate：该产品的上游产品或原材料类别，根据 product_desc 推断其主要来源、核心原料或关键部件。
downstream：该产品的下游应用或主要消费行业，根据 product_desc 推断其主要应用场景或典型终端产品。
推断时可参考该产品的行业链条结构：
上游（superordinate）：原材料、零部件、基础技术。
中游：产品本身的制造或加工环节。
下游（downstream）：应用行业、终端产品或消费者市场。
输出应简洁、专业，并符合产业链逻辑。
7. 若提取的"product_name"不为空，则"product_form"、"product_function"、"product_hs_code"三个字段禁止为空，根据"product_desc"内容进行合理推断。
8. 可以根据"product_name"推断该产品的功能"product_function"，"product_function"严禁为空，示例如下：
  Hardware → Data Storage
  Floor Tiles → Floor covering
  Heat Shrink Tubing → Wire insulation or Waterproofing
  Gasket Punch → Gasket Fabrication

## 输出结构及解释 ##
 请严格按照以下字段名输出 JSON 结构体，输出JSON**无需**任何换行
{
    "product_name": "",                     // 通用产品名（如 fireproof door seal）
    "material": "",                         // 材质（如 PVC, rubber）
    "specification": "",                    // 规格/尺寸（如 18x15mm）
    "product_form": "",                     // 产品形态（如 roll, sheet，Moulding，Strip，Pellet等常见海关申报中的商品形状）
    "product_quantity": ,                   // 产品数量
    "product_quantity_unit": "",                 // 产品数量单位（如 box，piece）
    "product_function": "",                 // 主要功能或主要作用（如 fireproof）
    "product_package": "",                // 产品包装
    "product_hs_code": "",                 // 产品所属海关贸易HS编码(WCO国际标准)
    "brand_name": "",            // 品牌名称
    "manufacturer_name":  "",   // 制造商名称(或可从产品描述提取)
    "product_category": "",                 // 产品类别（如 sealing parts）
    "product_tagged_keywords": [],           // 从描述中提取的关键词（如 ["seal", "PVC", "fireproof"]）
    "transport_mode_code": ,                   // 运输方式代码（WCO 世界海关组织的运输方式代码）
    "trade_weight": ,                 // 贸易总重量，单位: KG
    "trade_amount": ,                  // 贸易总价格，单位: 美元
    "likely_sensitive_goods": true/false,        // 是否可能为敏感商品（如军品、化工）
    "alias": [],
    "superordinate": [],
    "downstream": []
    }

## 强调 ##
1. 输入的"seller"**不是**制造商名称，当"product_desc"明确提及了制造商相关词汇（例如：manufacture/nhà sản xuất/fabricante等），直接将其对应的实体提取到"manufacturer_name"标签，无需任何推理。
2. 当"product_desc"没有明确提及制造商相关词汇时，则判断"brand_name"与"seller"是否为相同公司（若两公司字段公司主体名称部分相同，则认为是相同公司；若两公司字段字面上明显不同无任何交集，则认为是不同公司）：
- 是相同公司，则将"seller"输出到"manufacturer_name"标签
- 不是相同公司或无法提取"brand_name"（即"brand_name"为null），则"manufacturer_name"直接输出null，无需任何判断。
3. "product_desc"包含产品的大小，规格，尺寸，重量等信息时，提取至"specification"，不要遗漏。同理，"product_form"，"product_function"，"product_hs_code"，"product_package"等也不要遗漏。
"""

    def __init__(self):
        self.max_workers = 100

        # 字节的模型与 ID 映射、deerapi模型与 ID 映射
        self.model_map = {
            'doubao-1.5-lite-32k': 'ep-20250319210121-qrqb5',  # 字节的模型与 ID 映射
            'doubao-1.5-pro-32k': 'ep-20250718152907-4wqg9',  # 字节的模型与 ID 映射
            'doubao-seed-1.6-flash': 'ep-20250807170204-sszk7',  # 字节的模型与 ID 映射
            'doubao-seed-1.6': 'ep-20250717171553-wvgz8',  # 字节的模型与 ID 映射
            'deepseek-v3': 'ep-20250327095917-lf6x6',  # 字节的模型与 ID 映射
            'gpt-4o': 'gpt-4o',  # deerapi模型与 ID 映射
            'qwen3-8b': 'qwen3-8b',  # 阿里模型与 ID 映射
            'qwen-plus': 'qwen-plus',  # 阿里模型与 ID 映射
        }

        # 字节
        self.volcengine_base_url = 'https://ark.cn-beijing.volces.com/api/v3'
        self.volcengine_api_key = 'f28f39ba-d360-407e-bf42-37d65cf9d494'

        # deerapi
        self.deerapi_base_url = 'https://api.deerapi.com/v1'
        self.deerapi_api_key = 'sk-0Fm7hPbpKE8ouefNRdLZ7Dl441WGQHjvqt88cI5obqJ1PfIN'

        # ali
        self.ali_base_url = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
        self.ali_api_key = 'sk-3975e45859784b09812a26f43721ab11'

        self.openai_client = OpenAI(
            # 此为默认路径，您可根据业务所在地域进行配置
            base_url=self.volcengine_base_url,
            # 从环境变量中获取您的 API Key
            api_key=self.volcengine_api_key,
            timeout=1800,
            default_headers={
                "x-ark-moderation-scene": "skip-ark-moderation"
            }
        )

        self.previous_response_id = None

    def access_openai(self, model: str, input_text: str):
        # print(self.USER_PROMPT.format(input=input_text))
        if 'qwen' in model:
            extra_body = {
                "enable_thinking": False
            }
        else:
            extra_body = {"thinking": {"type": "disabled"}}
        try:
            s_t = time.time()
            completion = self.openai_client.chat.completions.create(
                model=model,
                temperature=0,
                top_p=0,
                messages=[
                    {"role": "user", "content": self.SYSTEM_PROMPT},
                    {"role": "user", "content": input_text},
                ],
                stream=False,
                extra_body=extra_body
            )
            print(f'{input_text=}')
            print(f'{completion=}')
            e_t = time.time()
            completion.usage.elapsed_time = e_t - s_t
            return completion
        except Exception:
            print(traceback.format_exc())
            return None

    def access_openai_caching(self, model: str, input_text: str):
        """
        缓存命中请求
        """
        if not self.previous_response_id:
            extra_body = {
                "thinking": {"type": "disabled"},
                "caching": {"type": "enabled"},
            }
            input = [
                {"role": "system", "content": self.SYSTEM_PROMPT},
            ]
        else:
            extra_body = {
                "thinking": {"type": "disabled"},
            }
            input = [
                {"role": "user", "content": input_text},
            ]

        for i in range(3):
            try:
                s_t = time.time()
                completion = self.openai_client.responses.create(
                    model=model,
                    temperature=0,
                    top_p=0,
                    input=input,
                    stream=False,
                    extra_body=extra_body,
                    previous_response_id=self.previous_response_id
                )
                # print(f'completion source={completion}')
                e_t = time.time()
                completion.usage.elapsed_time = e_t - s_t
                return completion
            except Exception:
                logging.error(f'input_text: {input_text[:100]} === retry no: {i+1} ===> {traceback.format_exc()}')
        return None

    def format_output(self, output: str):
        """
        对输出结果进行解析
        """
        if not output:
            return {}
        # 去除思考过程
        output = re.sub(r'<think>.*?</think>', '', output, flags=re.DOTALL)
        try:
            if '```json' in output:
                output = output.replace('```json', '').replace('```', '').strip()
            output = json.loads(output)
            return output
        except Exception:
            return {}

    def check_list_values(self, values: list):
        """校验列表类型的返回值，并返回为字符串"""
        if not values:
            return ''
        values_check = []
        for value in values:
            if not value or value in ['null']:
                continue
            values_check.append(value)
        if not values_check:
            return ''
        return json.dumps(values_check, ensure_ascii=False)

    def run_openai(self, model: str, req_datalist: list):
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_input = {}
            for data in req_datalist:
                input_text = json.dumps(data, default=str, ensure_ascii=False)
                future = executor.submit(self.access_openai, model, input_text)
                future_to_input[future] = input_text

            output_datalist = []
            for future in as_completed(future_to_input):
                input_text = future_to_input[future]
                input_data = json.loads(input_text)
                trade_uuid = input_data['uuid']
                try:
                    completion = future.result()
                    if not completion:
                        continue
                    output = completion.choices[0].message.content
                    # output = completion.output[0].content[0].text
                    # print(output)
                    # with open('completion_output.txt', 'a') as fp:
                    #     fp.write(output + '\n')
                    output_json = self.format_output(output)
                    elapsed_time = completion.usage.elapsed_time
                    total_tokens = completion.usage.total_tokens
                    completion_tokens = completion.usage.completion_tokens
                    # completion_tokens = completion.usage.output_tokens
                    prompt_tokens = completion.usage.prompt_tokens
                    # prompt_tokens = completion.usage.input_tokens
                    # cached_tokens = completion.usage.input_tokens_details.cached_tokens
                    output_json['trade_uuid'] = trade_uuid
                    output_json['elapsed_time'] = round(elapsed_time, 4)
                    output_json['total_tokens'] = total_tokens
                    output_json['completion_tokens'] = completion_tokens
                    output_json['prompt_tokens'] = prompt_tokens
                    # output_json['cached_tokens'] = cached_tokens
                except Exception:
                    print(trade_uuid, '===>', traceback.format_exc())
                    output_json = {}
                if not output_json:
                    continue
                output_datalist.append(output_json)
            return output_datalist

    def save_to_excel(self, records: list):
        """
        将records列表数据保存到Excel文件
        """
        if not records:
            logging.info("没有数据需要保存到Excel")
            return

        try:
            # 读取现有的Excel文件
            excel_file_path = 'out_data.xlsx'
            try:
                existing_df = pd.read_excel(excel_file_path)
                logging.info(f"读取到现有Excel文件，当前行数: {len(existing_df)}")
            except FileNotFoundError:
                # 如果文件不存在，创建一个空的DataFrame
                existing_df = pd.DataFrame()
                logging.info("Excel文件不存在，将创建新文件")

            # 将records转换为DataFrame
            new_df = pd.DataFrame(records)

            # 添加id列（自增）
            if len(existing_df) > 0:
                start_id = existing_df['id'].max() + 1 if 'id' in existing_df.columns else 1
            else:
                start_id = 1

            new_df.insert(0, 'id', range(start_id, start_id + len(new_df)))

            # 合并数据
            if len(existing_df) > 0:
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            else:
                combined_df = new_df

            # 保存到Excel文件
            combined_df.to_excel(excel_file_path, index=False)
            logging.info(f"成功保存 {len(records)} 条记录到Excel文件: {excel_file_path}")
            logging.info(f"Excel文件总行数: {len(combined_df)}")

        except Exception as e:
            logging.error(f"保存到Excel文件时出错: {str(e)}")
            logging.error(traceback.format_exc())

    def main(self):
        # model_id = "doubao-1.5-lite-32k"
        model_id = "doubao-1.5-pro-32k"
        # model_id = "doubao-seed-1.6-flash"
        # model_id = "doubao-seed-1.6"
        # model_id = "deepseek-v3"
        # model_id = "gpt-4o"
        # model_id = "qwen3-8b"
        # model_id = "qwen-plus"
        with open('海关贸易数据标注测试集V4.json', 'r', encoding='utf-8') as f:
            data_json = json.load(f)

        datalist = data_json.get('RECORDS', [])

        # if not self.previous_response_id:
        #     # 获取 previous_response_id 作为缓存命中
        #     completion = self.access_openai_caching(self.model_map[model_id], "")
        #     self.previous_response_id = completion.id

        records = []
        output_datalist = self.run_openai(self.model_map[model_id], datalist)
        for output_json in output_datalist:
            trade_uuid = output_json['trade_uuid']
            transport_mode_code = output_json.get('transport_mode_code', 0) or 0
            trade_weight = output_json.get('trade_weight', 0) or 0
            trade_amount = output_json.get('trade_amount', 0) or 0
            product_name = output_json.get('product_name', []) or []
            product_name = json.dumps(product_name, ensure_ascii=False) if product_name else ''
            product_material = output_json.get('material', '') or ''
            if isinstance(product_material, list):
                product_material = ','.join(product_material)
            product_specification = output_json.get('specification', '') or ''
            product_form = output_json.get('product_form', '') or ''
            if isinstance(product_form, list):
                product_form = ','.join(product_form)
            product_quantity = output_json.get('product_quantity', 0) or 0
            product_quantity_unit = output_json.get('product_quantity_unit', '') or ''
            product_hs_code = output_json.get('product_hs_code', '') or ''
            if isinstance(product_hs_code, list):
                product_hs_code = ','.join(product_hs_code)
            
            product_function = output_json.get('product_function', '') or ''
            if isinstance(product_function, list):
                product_function = ','.join(product_function)
            product_package = output_json.get('product_package', '') or ''
            brand_name = output_json.get('brand_name', '') or ''
            manufacturer_name = output_json.get('manufacturer_name', '') or ''
            product_category = output_json.get('product_category', '') or ''
            if isinstance(product_category, list):
                product_category = ','.join(product_category)
            product_tagged_keywords = output_json.get('product_tagged_keywords', []) or []
            product_tagged_keywords = self.check_list_values(product_tagged_keywords)
            likely_sensitive_goods = output_json.get('likely_sensitive_goods', False)
            likely_sensitive_goods = 1 if likely_sensitive_goods else 0
            product_alias = output_json.get('alias', []) or []
            product_superordinate = output_json.get('superordinate', []) or []
            product_downstream = output_json.get('downstream', []) or []
            product_alias = self.check_list_values(product_alias)
            product_superordinate = self.check_list_values(product_superordinate)
            product_downstream = self.check_list_values(product_downstream)
            elapsed_time = output_json.get('elapsed_time', 0) or 0
            total_tokens = output_json.get('total_tokens', 0) or 0
            completion_tokens = output_json.get('completion_tokens', 0) or 0
            prompt_tokens = output_json.get('prompt_tokens', 0) or 0
            # cached_tokens = output_json.get('cached_tokens', 0) or 0

            record_data = {
                "trade_uuid": trade_uuid,
                "model_name": model_id,
                "model_tokens": total_tokens,
                "completion_tokens": completion_tokens,
                "prompt_tokens": prompt_tokens,
                # "cached_tokens": cached_tokens,
                "time_consuming": elapsed_time,
                "product_name": product_name,
                "product_material": product_material,
                "product_specification": product_specification,
                "product_form": product_form,
                "product_quantity": product_quantity,
                "product_quantity_unit": product_quantity_unit,
                "product_function": product_function,
                "product_package": product_package,
                "product_hs_code": product_hs_code,
                "brand_name": brand_name,
                "manufacturer_name": manufacturer_name,
                "product_category": product_category,
                "product_tagged_keywords": product_tagged_keywords,
                "transport_mode_code": transport_mode_code,
                "trade_weight": trade_weight,
                "trade_amount": trade_amount,
                "likely_sensitive_goods": likely_sensitive_goods,
                "product_alias": product_alias,
                "product_superordinate": product_superordinate,
                "product_downstream": product_downstream,
            }
            print(record_data)
            record_data_model = TagsTrade(**record_data)
            records.append(record_data_model.__dict__)

        # 数据存储到Excel
        self.save_to_excel(records)
        # self.tb_tags_trade_test_obj.save(records)

    def test_unit(self):
        datalist = [{
            "uuid": "202502001a64f769fbb3fe7e11e7af10d5254a",
            "weight": "28",
            "weight_unit": "",
            "quantity": "27",
            "quantity_unit": "",
            "price": "167",
            "amount": "4500",
            "amount_unit": "USD",
            "product_desc": "6PCS BELT WITH JOINT LOWER TEFLON BELT SEAMLESS UPPER BELT INV R250123001 TEXTILE MACHINE SPARE PART",
            "product_hscode": "40103900",
            "seller": "OSHIMA",
            "notifier": "",
            "container": "",
            "transport": ""
        }]
        # model_id = "doubao-1.5-lite-32k"
        model_id = "doubao-1.5-pro-32k"
        # model_id = "doubao-seed-1.6-flash"
        # model_id = "doubao-seed-1.6"
        # model_id = "deepseek-v3"
        # model_id = "gpt-4o"
        # model_id = "qwen3-8b"
        # model_id = "qwen-plus"
        output_datalist = self.run_openai(self.model_map[model_id], datalist)
        print(output_datalist)


    def process_ai_response_to_excel(self, ai_response_json_str: str, trade_uuid: str = None):
        """
        处理AI返回的JSON数据并写入Excel

        Args:
            ai_response_json_str: AI返回的JSON字符串
            trade_uuid: 交易UUID，如果不提供则自动生成
        """
        try:
            # 解析AI返回的JSON数据
            ai_data = json.loads(ai_response_json_str)

            # 如果没有提供trade_uuid，生成一个
            if not trade_uuid:
                import uuid
                trade_uuid = str(uuid.uuid4())

            # 转换为Excel格式的记录，处理null值
            def safe_get_str(key, default=""):
                value = ai_data.get(key, default)
                return value if value is not None else default

            def safe_get_int(key, default=0):
                value = ai_data.get(key, default)
                return value if value is not None else default

            def safe_get_float(key, default=0.0):
                value = ai_data.get(key, default)
                return value if value is not None else default

            # 处理可能是列表的字段
            def safe_get_str_or_list(key, default=""):
                value = ai_data.get(key, default)
                if value is None:
                    return default
                elif isinstance(value, list):
                    return self.check_list_values(value)
                else:
                    return str(value)

            record_data = {
                "trade_uuid": trade_uuid,
                "product_name": safe_get_str_or_list("product_name"),
                "product_material": safe_get_str("material"),
                "product_specification": safe_get_str("specification"),
                "product_form": safe_get_str("product_form"),
                "product_quantity": safe_get_int("product_quantity"),
                "product_quantity_unit": safe_get_str("product_quantity_unit"),
                "product_function": safe_get_str_or_list("product_function"),  # 修复：支持列表格式
                "product_package": safe_get_str("product_package"),
                "product_hs_code": safe_get_str("product_hs_code"),
                "brand_name": safe_get_str("brand_name"),
                "product_category": safe_get_str("product_category"),
                "product_tagged_keywords": self.check_list_values(ai_data.get("product_tagged_keywords", [])),
                "transport_mode_code": safe_get_int("transport_mode_code"),
                "trade_weight": safe_get_float("trade_weight"),
                "trade_amount": safe_get_float("trade_amount"),
                "likely_sensitive_goods": 1 if ai_data.get("likely_sensitive_goods", False) else 0,
                "product_alias": self.check_list_values(ai_data.get("alias", [])),
                "product_superordinate": self.check_list_values(ai_data.get("superordinate", [])),
                "product_downstream": self.check_list_values(ai_data.get("downstream", []))
            }

            # 创建TagsTrade模型实例进行验证
            record_data_model = TagsTrade(**record_data)
            records = [record_data_model.__dict__]

            # 保存到Excel
            self.save_to_excel(records)

            logging.info(f"成功处理AI响应数据并写入Excel，trade_uuid: {trade_uuid}")
            return True

        except Exception as e:
            logging.error(f"处理AI响应数据时出错: {str(e)}")
            logging.error(traceback.format_exc())
            return False

    def test_excel_write(self):
        """测试Excel写入功能"""
        # 测试包含列表类型product_function的AI返回数据
        ai_response_with_list_function = '{"product_name": ["Door Seal"], "material": "Rubber", "specification": "18x15mm", "product_form": "Strip", "product_quantity": 100, "product_quantity_unit": "meters", "product_function": ["Door and window installation", "Lighting"], "product_package": "Roll", "product_hs_code": "401699", "brand_name": "Generic", "manufacturer_name": null, "product_category": "Sealing Parts", "product_tagged_keywords": ["Door", "Seal", "Rubber"], "transport_mode_code": 1, "trade_weight": 5.2, "trade_amount": 150, "likely_sensitive_goods": false, "alias": ["Weather Strip", "Door Gasket"], "superordinate": ["Rubber", "Polymer"], "downstream": ["Construction", "Automotive"]}'

        # 处理AI响应并写入Excel
        success = self.process_ai_response_to_excel(ai_response_with_list_function, "test_list_function_uuid")

        if success:
            print("✅ 包含列表类型product_function的数据处理成功！")
        else:
            print("❌ 数据处理失败！")

        # 再测试一个字符串类型的product_function
        ai_response_with_string_function = '{"product_name": ["Lab Grown Diamond"], "material": "Lab Grown Diamond", "specification": "CUT AND POLISHED, FANCY INTENSE YELLOW VS1", "product_form": "Piece", "product_quantity": 2, "product_quantity_unit": "", "product_function": "Jewelry Making", "product_package": "Piece", "product_hs_code": "710491", "brand_name": "NIVODA", "product_category": "Jewelry", "product_tagged_keywords": ["Lab Grown Diamond"], "transport_mode_code": null, "trade_weight": 0, "trade_amount": 397, "likely_sensitive_goods": false, "alias": ["Synthetic Diamond"], "superordinate": ["Carbon"], "downstream": ["Rings"]}'

        success2 = self.process_ai_response_to_excel(ai_response_with_string_function, "test_string_function_uuid")

        if success2:
            print("✅ 包含字符串类型product_function的数据处理成功！")
        else:
            print("❌ 数据处理失败！")


if __name__ == '__main__':
    TagsTradeTest().main()
    # TagsTradeTest().test_unit()
    # TagsTradeTest().test_excel_write()

