# AI数据打标Excel导出功能

## 功能概述

已为海关贸易数据打标项目添加了Excel导出功能，可以将AI返回的JSON数据自动解析并写入Excel文档。

## 主要功能

### 1. 自动Excel写入
- 将AI返回的JSON数据解析并转换为Excel格式
- 自动处理null值和数据类型转换
- 支持增量写入，新数据会追加到现有Excel文件中
- 自动生成递增的ID列

### 2. 数据字段映射
AI返回的JSON字段会自动映射到Excel列：

| AI JSON字段 | Excel列名 | 说明 |
|------------|-----------|------|
| product_name | product_name | 产品名称（数组转字符串） |
| material | product_material | 产品材质 |
| specification | product_specification | 产品规格 |
| product_form | product_form | 产品形态 |
| product_quantity | product_quantity | 产品数量 |
| product_function | product_function | 产品功能 |
| product_hs_code | product_hs_code | HS编码 |
| brand_name | brand_name | 品牌名称 |
| product_category | product_category | 产品类别 |
| transport_mode_code | transport_mode_code | 运输方式代码 |
| trade_weight | trade_weight | 贸易重量 |
| trade_amount | trade_amount | 贸易金额 |
| likely_sensitive_goods | likely_sensitive_goods | 是否敏感商品 |
| alias | product_alias | 产品别名 |
| superordinate | product_superordinate | 上游原料 |
| downstream | product_downstream | 下游应用 |

## 使用方法

### 方法1：直接调用处理函数

```python
from tmp_tags_trade_test_v2 import TagsTradeTest

# 创建处理器实例
processor = TagsTradeTest()

# AI返回的JSON字符串
ai_response_json = '''{"product_name": ["Lab Grown Diamond"], "material": "Lab Grown Diamond", ...}'''

# 处理并写入Excel
trade_uuid = "your_trade_uuid_here"
success = processor.process_ai_response_to_excel(ai_response_json, trade_uuid)

if success:
    print("✅ 数据已成功写入Excel!")
else:
    print("❌ 写入失败!")
```

### 方法2：在现有流程中集成

在`tmp_tags_trade_test_v2.py`的`main()`方法中，数据处理完成后会自动调用`save_to_excel(records)`将数据写入Excel文件。

### 方法3：批量处理

参考`process_ai_response_example.py`中的批量处理示例。

## 输出文件

- **文件名**: `out_data.xlsx`
- **位置**: 项目根目录
- **格式**: Excel工作簿，包含所有必要的列
- **特点**: 支持增量写入，每次运行会追加新数据

## 数据处理特性

1. **Null值处理**: 自动将JSON中的null值转换为适当的默认值
2. **数组字段**: 将数组类型字段转换为JSON字符串格式存储
3. **数据验证**: 使用Pydantic模型进行数据验证
4. **错误处理**: 完整的异常处理和日志记录
5. **自动ID**: 为每条记录自动生成递增ID

## 示例数据

输入的AI JSON响应：
```json
{
    "product_name": ["Lab Grown Diamond"],
    "material": "Lab Grown Diamond",
    "specification": "CUT AND POLISHED, FANCY INTENSE YELLOW VS1",
    "trade_amount": 397,
    "likely_sensitive_goods": false,
    "alias": ["Synthetic Diamond", "Lab Created Diamond"]
}
```

Excel中的对应记录：
| id | trade_uuid | product_name | product_material | trade_amount | likely_sensitive_goods |
|----|------------|--------------|------------------|--------------|----------------------|
| 1  | uuid_001   | ["Lab Grown Diamond"] | Lab Grown Diamond | 397 | 0 |

## 日志信息

程序运行时会输出详细的日志信息：
- 读取现有Excel文件状态
- 数据处理进度
- 保存结果统计
- 错误信息（如有）

## 注意事项

1. 确保已安装必要的依赖：`pandas`、`openpyxl`
2. Excel文件会在首次运行时自动创建
3. 每次运行都会追加新数据，不会覆盖现有数据
4. 建议定期备份Excel文件
